{"contentLength":"599","duration":"73ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:08:57","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:08:57","url":"/unknown-route"}
{"contentLength":"599","duration":"54ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:10:24","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:10:24","url":"/unknown-route"}
{"contentLength":"599","duration":"73ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:15:47","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:15:47","url":"/unknown-route"}
{"contentLength":"599","duration":"59ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:20:51","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:20:51","url":"/unknown-route"}
{"contentLength":"599","duration":"82ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:22:04","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:22:04","url":"/unknown-route"}
{"contentLength":"599","duration":"59ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:25:52","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:25:52","url":"/unknown-route"}
{"contentLength":"85","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 17:31:16","url":"/auth/"}
{"contentLength":"604","duration":"52ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:31:26","url":"/health"}
{"contentLength":"606","duration":"136ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:32:46","url":"/health"}
{"contentLength":"605","duration":"35ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:33:26","url":"/health"}
{"contentLength":"1470","duration":"131ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:33:27","url":"/auth/register"}
{"contentLength":"238","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 17:33:27","url":"/auth/register"}
{"contentLength":"606","duration":"254ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 03:57:28","url":"/health"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 03:57:29","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-16 03:57:29"}
{"contentLength":"114","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 03:57:29","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND auth-service","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-15 21:51:05"}
{"contentLength":"118","duration":"3955ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 21:51:05","url":"/auth/register"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND auth-service","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-15 21:51:09"}
{"contentLength":"118","duration":"3940ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 21:51:09","url":"/auth/login"}
{"contentLength":"650","duration":"53ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 22:31:59","url":"/health"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 22:32:32","url":"/auth/health"}
{"contentLength":"86","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 22:32:32","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 22:32:32","url":"/archive/health"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"108","duration":"42ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-15 22:32:33","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:08:19","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:19","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:19","url":"/archive/health"}
{"contentLength":"375","duration":"23ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:08:19","url":"/auth/register"}
{"contentLength":"91","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:08:41","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:41","url":"/assessments/health"}
{"contentLength":"86","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:41","url":"/archive/health"}
{"contentLength":"375","duration":"15ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:08:41","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:08:41","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:08:43"}
{"contentLength":"114","duration":"2547ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:08:43","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:10:34","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:10:34","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:10:34","url":"/archive/health"}
{"contentLength":"375","duration":"21ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:10:34","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:10:34","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:10:37"}
{"contentLength":"114","duration":"2543ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:10:37","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:11:17","url":"/auth/health"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:11:17","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:11:17","url":"/archive/health"}
{"contentLength":"375","duration":"20ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:11:17","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:11:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:11:20"}
{"contentLength":"114","duration":"2535ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:11:20","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:13:02","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/archive/health"}
{"contentLength":"375","duration":"17ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:13:02","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:13:02","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"49ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:13:51","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/archive/health"}
{"contentLength":"375","duration":"16ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:13:51","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:13:51","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"52ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:14:45","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/assessments/health"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/archive/health"}
{"contentLength":"375","duration":"51ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:14:45","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:14:45","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"57ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:16:17","url":"/auth/health"}
{"contentLength":"86","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:16:17","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:16:17","url":"/archive/health"}
{"contentLength":"375","duration":"47ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:16:17","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:20:58","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:20:58","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:20:58","url":"/archive/health"}
{"contentLength":"375","duration":"28ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:20:58","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:27:52","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:27:52","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:27:52","url":"/archive/health"}
{"contentLength":"375","duration":"31ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:27:52","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:32:48","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:48","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:48","url":"/archive/health"}
{"contentLength":"82","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:49","url":"/assessments/submit"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:34:09","url":"/auth/health"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/archive/health"}
{"contentLength":"375","duration":"28ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:34:09","url":"/auth/register"}
{"contentLength":"82","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/assessments/submit"}
