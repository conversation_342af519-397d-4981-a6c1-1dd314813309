# API Gateway Service

Service yang bertindak sebagai single entry point untuk semua request dari frontend ke microservices backend dalam ekosistem ATMA.

## 🚀 Fungsi Utama

1. **Routing**: Meneruskan request ke service yang tepat (auth, assessment, archive)
2. **Authentication**: Validasi JWT token dan user context
3. **Rate Limiting**: Pembatasan request per user/IP dengan konfigurasi yang fleksibel
4. **Logging**: Centralized logging dengan Winston untuk monitoring dan debugging
5. **CORS**: Cross-origin resource sharing dengan konfigurasi yang aman
6. **Security**: Helmet untuk security headers dan request validation
7. **Health Checks**: Monitoring status semua microservices
8. **Error Handling**: Centralized error handling dengan response yang konsisten

## 📋 Prerequisites

- Node.js 18+ dan npm 8+
- Akses ke microservices: auth-service, assessment-service, archive-service
- Environment variables yang dikonfigurasi dengan benar

## 🛠️ Installation

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Setup Environment**
   ```bash
   cp .env.example .env
   # Edit .env sesuai dengan konfigurasi environment Anda
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Run Tests**
   ```bash
   npm test
   npm run test:coverage
   ```

5. **Health Check**
   ```bash
   npm run health
   ```

## ⚙️ Environment Variables

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# Service URLs
AUTH_SERVICE_URL=http://localhost:3001
ARCHIVE_SERVICE_URL=http://localhost:3002
ASSESSMENT_SERVICE_URL=http://localhost:3003
NOTIFICATION_SERVICE_URL=http://localhost:3005

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/api-gateway.log
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   Frontend      │────│  API Gateway    │
│   Application   │    │  (Port: 3000)   │
└─────────────────┘    └─────────┬───────┘
                                 │
                    ┌────────────┼────────────┐
                    │            │            │
            ┌───────▼──────┐ ┌───▼────┐ ┌────▼──────┐
            │ Auth Service │ │Assessment│ │  Archive  │
            │ (Port: 3001) │ │ Service  │ │  Service  │
            │              │ │(Port:3003)│ │(Port:3002)│
            └──────────────┘ └─────────┘ └───────────┘
```

## 🛣️ API Routes

### 🔐 Authentication Routes
Semua request authentication diteruskan ke Auth Service (http://localhost:3001)

#### POST /auth/register
Registrasi user baru dengan rate limiting khusus (5 requests per 15 menit per IP)

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "token_balance": 5,
      "created_at": "2024-01-01T00:00:00Z"
    },
    "token": "jwt_token_here"
  }
}
```

#### POST /auth/login
**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "token_balance": 5
    },
    "token": "jwt_token_here"
  }
}
```

#### GET /auth/profile
**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "token_balance": 5,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

### 📊 Assessment Routes
Semua request assessment diteruskan ke Assessment Service (http://localhost:3003)
**Requires Authentication**: Semua endpoint memerlukan JWT token
**Rate Limiting**: 10 submissions per hour per user

#### POST /assessments/submit
Submit data assessment untuk diproses oleh AI

**Headers:**
```
Authorization: Bearer jwt_token_here
Content-Type: application/json
```

**Request Body:**
```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    "loveOfLearning": 82,
    "perspective": 60,
    "bravery": 55,
    "perseverance": 68,
    "honesty": 73,
    "zest": 66,
    "love": 80,
    "kindness": 75,
    "socialIntelligence": 65,
    "teamwork": 60,
    "fairness": 70,
    "leadership": 67,
    "forgiveness": 58,
    "humility": 62,
    "prudence": 69,
    "selfRegulation": 61,
    "appreciationOfBeauty": 50,
    "gratitude": 72,
    "hope": 77,
    "humor": 65,
    "spirituality": 55
  },
  "multipleIntelligences": {
    "linguistic": 85,
    "logicalMathematical": 90,
    "spatial": 75,
    "bodilyKinesthetic": 60,
    "musical": 55,
    "interpersonal": 70,
    "intrapersonal": 65,
    "naturalistic": 50
  },
  "cognitiveStyleIndex": {
    "analytic": 80,
    "intuitive": 60
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Assessment submitted successfully",
  "data": {
    "jobId": "uuid",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes"
  }
}
```

#### GET /assessments/status/:jobId
Check status assessment yang sedang diproses

**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "uuid",
    "status": "processing|completed|failed",
    "progress": 75,
    "estimatedTimeRemaining": "1-2 minutes"
  }
}
```

### 📁 Archive Routes
Semua request archive diteruskan ke Archive Service (http://localhost:3002)
**Requires Authentication**: Semua endpoint memerlukan JWT token

#### GET /archive/results
Mendapatkan daftar hasil analisis user dengan pagination

**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "uuid",
        "user_id": "uuid",
        "persona_profile": [...],
        "status": "completed",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

#### GET /archive/results/:id
**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "user_id": "uuid",
    "assessment_data": {...},
    "persona_profile": [...],
    "status": "completed",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

## 🏥 Health Check Endpoints

#### GET /health
Comprehensive health check untuk semua services

**Response:**
```json
{
  "status": "healthy|degraded|error",
  "timestamp": "2024-01-01T00:00:00Z",
  "responseTime": "45ms",
  "version": "1.0.0",
  "services": {
    "auth-service": {
      "status": "healthy",
      "responseTime": "12ms",
      "statusCode": 200
    },
    "assessment-service": {
      "status": "healthy",
      "responseTime": "8ms",
      "statusCode": 200
    },
    "archive-service": {
      "status": "healthy",
      "responseTime": "15ms",
      "statusCode": 200
    },
    "notification-service": {
      "status": "unhealthy",
      "error": "Connection refused",
      "code": "ECONNREFUSED"
    }
  },
  "gateway": {
    "status": "healthy",
    "uptime": 3600,
    "memory": {
      "heapUsed": 45678912,
      "heapTotal": 67108864
    },
    "nodeVersion": "v18.17.0"
  }
}
```

#### GET /health/detailed
Extended health check dengan informasi sistem

#### GET /health/live
Simple liveness probe (untuk Kubernetes)

#### GET /health/ready
Readiness probe - checks critical services

## ❌ Error Responses

Semua error responses menggunakan format yang konsisten:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message"
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "unique-request-id"
}
```

### Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `UNAUTHORIZED` | 401 | Invalid or expired token |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `VALIDATION_ERROR` | 400 | Invalid request data |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `SERVICE_UNAVAILABLE` | 503 | Microservice unavailable |
| `INTERNAL_ERROR` | 500 | Server error |

## 🚦 Rate Limiting

API Gateway mengimplementasikan rate limiting untuk mencegah abuse:

- **General Rate Limit**:
  - Window: 15 minutes
  - Max Requests: 100 per window per IP
  - Headers: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`

- **Auth Endpoints Rate Limit**:
  - Window: 15 minutes
  - Max Requests: 5 per window per IP
  - Applies to: `/auth/register`, `/auth/login`

- **Assessment Endpoints Rate Limit**:
  - Window: 60 minutes
  - Max Requests: 10 per window per user
  - Applies to: `/assessments/submit`

## 📝 Logging

API Gateway mengimplementasikan comprehensive logging dengan Winston:

- **Request Logging**:
  - Timestamp
  - Method dan URL
  - User ID (jika authenticated)
  - Request headers dan body
  - IP address dan User Agent

- **Response Logging**:
  - Status code
  - Response time
  - Response size
  - Error details (jika ada)

- **Security Logging**:
  - Authentication events
  - Rate limit violations
  - Suspicious activities

- **Log Formats**:
  - Development: Colored console output
  - Production: JSON format untuk analisis

## 🔄 Proxy Configuration

API Gateway menggunakan `express-http-proxy` untuk meneruskan requests ke microservices dengan:

- Request transformation
- Response transformation
- Error handling
- Timeout management
- Header forwarding
- User context propagation

## 🚀 Production Deployment

### Environment Configuration
Make sure to set the following environment variables in production:

```bash
NODE_ENV=production
PORT=3000
JWT_SECRET=your_secure_jwt_secret_key
AUTH_SERVICE_URL=http://localhost:3001
ASSESSMENT_SERVICE_URL=http://localhost:3003
ARCHIVE_SERVICE_URL=http://localhost:3002
```

### Start in Production
```bash
npm start
```

## 🧪 Testing

### Run Tests
```bash
# Unit tests
npm test

# Watch mode
npm run test:watch

# Coverage report
npm run test:coverage
```

### Test Structure
```
tests/
├── app.test.js              # Main application tests
├── middleware/
│   ├── auth.test.js         # Authentication middleware tests
│   ├── rateLimiter.test.js  # Rate limiting tests
│   └── errorHandler.test.js # Error handling tests
├── routes/
│   ├── health.test.js       # Health check tests
│   └── proxy.test.js        # Proxy functionality tests
└── setup.js                 # Test setup and mocks
```

## 🚀 Deployment

### Production Checklist

- [ ] Set strong `JWT_SECRET`
- [ ] Configure proper `CORS_ORIGIN`
- [ ] Set appropriate rate limits
- [ ] Configure log levels
- [ ] Set up monitoring
- [ ] Configure health checks
- [ ] Set up SSL/TLS termination
- [ ] Configure reverse proxy (nginx/traefik)

### Environment-specific Configuration

**Development:**
```env
NODE_ENV=development
LOG_LEVEL=debug
CORS_ORIGIN=http://localhost:3000
```

**Production:**
```env
NODE_ENV=production
LOG_LEVEL=info
CORS_ORIGIN=https://yourdomain.com
```

## 📊 Monitoring

### Metrics
- Request count per endpoint
- Response times
- Error rates
- Rate limit violations
- Service health status

### Logs
- Access logs (Morgan)
- Application logs (Winston)
- Error logs with stack traces
- Security events

### Health Checks
- Liveness probe: `/health/live`
- Readiness probe: `/health/ready`
- Detailed health: `/health/detailed`

## 🔧 Development

### Project Structure
```
api-gateway/
├── src/
│   ├── app.js                 # Main application
│   ├── config/
│   │   └── services.js        # Service configurations
│   ├── middleware/
│   │   ├── auth.js           # JWT authentication
│   │   ├── rateLimiter.js    # Rate limiting
│   │   ├── requestLogger.js  # Request logging
│   │   └── errorHandler.js   # Error handling
│   ├── routes/
│   │   ├── auth.js           # Auth proxy routes
│   │   ├── assessment.js     # Assessment proxy routes
│   │   ├── archive.js        # Archive proxy routes
│   │   └── health.js         # Health check routes
│   └── utils/
│       ├── logger.js         # Winston logger setup
│       └── requestId.js      # Request ID middleware
├── tests/                     # Test files
├── scripts/                   # Utility scripts
├── logs/                      # Log files
├── package.json
├── .env.example
└── README.md
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
