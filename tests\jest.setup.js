// Global Jest Setup for ATMA Backend Testing
// This file runs before all tests and sets up the testing environment

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only_do_not_use_in_production';
process.env.INTERNAL_SERVICE_KEY = 'test_internal_service_key_for_testing_only';

// Database configuration for testing
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5433';
process.env.DB_NAME = 'atma_test_db';
process.env.DB_USER = 'postgres';
process.env.DB_PASSWORD = 'test';
process.env.DB_DIALECT = 'postgres';

// RabbitMQ configuration for testing
process.env.RABBITMQ_URL = 'amqp://localhost:5673';
process.env.RABBITMQ_USER = 'guest';
process.env.RABBITMQ_PASSWORD = 'guest';
process.env.EXCHANGE_NAME = 'atma_test_exchange';
process.env.QUEUE_NAME = 'test_assessment_analysis';
process.env.ROUTING_KEY = 'test.analysis.process';
process.env.DEAD_LETTER_QUEUE = 'test_assessment_analysis_dlq';

// Service URLs for testing
process.env.AUTH_SERVICE_URL = 'http://localhost:3001';
process.env.ARCHIVE_SERVICE_URL = 'http://localhost:3002';
process.env.ASSESSMENT_SERVICE_URL = 'http://localhost:3003';
process.env.NOTIFICATION_SERVICE_URL = 'http://localhost:3005';

// API Gateway configuration
process.env.PORT = '3000';
process.env.CORS_ORIGIN = 'http://localhost:3000,http://localhost:3001';
process.env.RATE_LIMIT_WINDOW_MS = '900000';
process.env.RATE_LIMIT_MAX_REQUESTS = '100';

// Logging configuration for testing
process.env.LOG_LEVEL = 'error'; // Reduce log noise during testing
process.env.LOG_FILE = 'logs/test.log';

// Google AI configuration for testing (mock)
process.env.GOOGLE_AI_API_KEY = 'test_google_ai_api_key_for_testing';
process.env.GOOGLE_AI_MODEL = 'gemini-2.5-flash';
process.env.AI_TEMPERATURE = '0.7';
process.env.AI_MAX_TOKENS = '4096';

// Worker configuration for testing
process.env.WORKER_CONCURRENCY = '1'; // Single worker for testing
process.env.MAX_RETRIES = '2'; // Reduced retries for faster testing
process.env.RETRY_DELAY = '1000'; // 1 second retry delay
process.env.PROCESSING_TIMEOUT = '30000'; // 30 seconds timeout
process.env.HEARTBEAT_INTERVAL = '10000'; // 10 seconds heartbeat

// Token configuration
process.env.DEFAULT_TOKEN_BALANCE = '5';
process.env.ANALYSIS_TOKEN_COST = '1';
process.env.JWT_EXPIRES_IN = '1h';
process.env.BCRYPT_ROUNDS = '4'; // Reduced for faster testing

// Pagination configuration
process.env.DEFAULT_PAGE_SIZE = '10';
process.env.MAX_PAGE_SIZE = '100';

// Queue configuration
process.env.QUEUE_DURABLE = 'false'; // Non-durable for testing
process.env.MESSAGE_PERSISTENT = 'false'; // Non-persistent for testing

// Extend Jest matchers
expect.extend({
  toBeValidJWT(received) {
    const jwt = require('jsonwebtoken');
    try {
      jwt.verify(received, process.env.JWT_SECRET);
      return {
        message: () => `Expected ${received} not to be a valid JWT`,
        pass: true
      };
    } catch (error) {
      return {
        message: () => `Expected ${received} to be a valid JWT, but got error: ${error.message}`,
        pass: false
      };
    }
  },

  toBeValidUUID(received) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = uuidRegex.test(received);
    
    if (pass) {
      return {
        message: () => `Expected ${received} not to be a valid UUID`,
        pass: true
      };
    } else {
      return {
        message: () => `Expected ${received} to be a valid UUID`,
        pass: false
      };
    }
  },

  toBeValidEmail(received) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const pass = emailRegex.test(received);
    
    if (pass) {
      return {
        message: () => `Expected ${received} not to be a valid email`,
        pass: true
      };
    } else {
      return {
        message: () => `Expected ${received} to be a valid email`,
        pass: false
      };
    }
  },

  toHaveValidAssessmentStructure(received) {
    const requiredFields = ['riasec', 'ocean', 'via_is', 'multiple_intelligences', 'cognitive_style'];
    const hasAllFields = requiredFields.every(field => received.hasOwnProperty(field));
    
    if (hasAllFields) {
      return {
        message: () => `Expected assessment data not to have valid structure`,
        pass: true
      };
    } else {
      const missingFields = requiredFields.filter(field => !received.hasOwnProperty(field));
      return {
        message: () => `Expected assessment data to have valid structure, missing fields: ${missingFields.join(', ')}`,
        pass: false
      };
    }
  },

  toHaveValidPersonaProfile(received) {
    const requiredFields = ['riasec_scores', 'ocean_scores', 'career_recommendations'];
    const hasAllFields = requiredFields.every(field => received.hasOwnProperty(field));
    
    if (hasAllFields) {
      return {
        message: () => `Expected persona profile not to have valid structure`,
        pass: true
      };
    } else {
      const missingFields = requiredFields.filter(field => !received.hasOwnProperty(field));
      return {
        message: () => `Expected persona profile to have valid structure, missing fields: ${missingFields.join(', ')}`,
        pass: false
      };
    }
  }
});

// Global test utilities
global.testUtils = {
  // Generate test user data
  generateTestUser: (overrides = {}) => ({
    email: `test-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    fullName: 'Test User',
    tokenBalance: 5,
    ...overrides
  }),

  // Generate test assessment data
  generateTestAssessment: () => ({
    riasec: {
      realistic: [4, 5, 3, 4, 5],
      investigative: [5, 4, 5, 3, 4],
      artistic: [3, 2, 4, 5, 3],
      social: [5, 5, 4, 5, 4],
      enterprising: [4, 3, 5, 4, 3],
      conventional: [2, 3, 2, 3, 4]
    },
    ocean: {
      openness: [4, 5, 3, 4, 5],
      conscientiousness: [5, 4, 5, 4, 5],
      extraversion: [3, 4, 3, 5, 4],
      agreeableness: [5, 5, 4, 5, 4],
      neuroticism: [2, 3, 2, 3, 2]
    },
    via_is: {
      wisdom: [4, 5, 4],
      courage: [5, 4, 5],
      humanity: [4, 5, 4],
      justice: [5, 5, 4],
      temperance: [4, 4, 5],
      transcendence: [5, 4, 4]
    },
    multiple_intelligences: {
      linguistic: [4, 5, 4],
      logical_mathematical: [5, 4, 5],
      spatial: [3, 4, 3],
      musical: [2, 3, 4],
      bodily_kinesthetic: [4, 3, 4],
      interpersonal: [5, 5, 4],
      intrapersonal: [4, 5, 5],
      naturalistic: [3, 4, 3]
    },
    cognitive_style: {
      knowing: [4, 5, 4],
      planning: [5, 4, 5],
      creating: [4, 3, 4]
    }
  }),

  // Generate mock AI response
  generateMockAIResponse: () => ({
    persona_profile: {
      riasec_scores: {
        realistic: 3.8,
        investigative: 4.2,
        artistic: 3.4,
        social: 4.6,
        enterprising: 3.8,
        conventional: 2.8
      },
      ocean_scores: {
        openness: 4.2,
        conscientiousness: 4.6,
        extraversion: 3.8,
        agreeableness: 4.6,
        neuroticism: 2.4
      },
      via_is_scores: {
        wisdom: 4.3,
        courage: 4.7,
        humanity: 4.3,
        justice: 4.7,
        temperance: 4.3,
        transcendence: 4.3
      },
      multiple_intelligences_scores: {
        linguistic: 4.3,
        logical_mathematical: 4.7,
        spatial: 3.3,
        musical: 3.0,
        bodily_kinesthetic: 3.7,
        interpersonal: 4.7,
        intrapersonal: 4.7,
        naturalistic: 3.3
      },
      cognitive_style_scores: {
        knowing: 4.3,
        planning: 4.7,
        creating: 3.7
      },
      career_recommendations: [
        {
          title: "Software Developer",
          match_percentage: 85,
          reasoning: "High investigative and realistic scores indicate strong problem-solving abilities"
        },
        {
          title: "Data Scientist",
          match_percentage: 82,
          reasoning: "Strong logical-mathematical intelligence and investigative traits"
        }
      ],
      personality_summary: "You are a thoughtful and analytical person with strong problem-solving abilities...",
      strengths: ["Analytical thinking", "Problem solving", "Attention to detail"],
      development_areas: ["Public speaking", "Networking", "Creative expression"],
      learning_style: "You prefer structured learning with hands-on practice and logical progression.",
      work_environment: "You thrive in quiet, organized environments with minimal distractions."
    }
  }),

  // Wait for async operations
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  // Generate random string
  randomString: (length = 10) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
};

// Console override for cleaner test output
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: process.env.NODE_ENV === 'test' ? () => {} : originalConsole.log,
  info: process.env.NODE_ENV === 'test' ? () => {} : originalConsole.info,
  warn: originalConsole.warn,
  error: originalConsole.error
};

// Unhandled promise rejection handler
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Uncaught exception handler
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

console.log('Jest setup completed for ATMA Backend testing environment');
