#!/bin/bash

# ATMA Backend Test Runner Script
# This script runs comprehensive tests for all microservices

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICES=("api-gateway" "auth-service" "assessment-service" "analysis-worker" "archive-service")
TEST_DB_NAME="atma_test_db"
TEST_RABBITMQ_CONTAINER="test-rabbitmq"
TEST_POSTGRES_CONTAINER="test-postgres"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to setup test environment
setup_test_environment() {
    print_status "Setting up test environment..."
    
    # Check required tools
    if ! command_exists docker; then
        print_error "Docker is required but not installed"
        exit 1
    fi
    
    if ! command_exists node; then
        print_error "Node.js is required but not installed"
        exit 1
    fi
    
    # Stop existing test containers
    docker stop $TEST_POSTGRES_CONTAINER $TEST_RABBITMQ_CONTAINER 2>/dev/null || true
    docker rm $TEST_POSTGRES_CONTAINER $TEST_RABBITMQ_CONTAINER 2>/dev/null || true
    
    # Start test PostgreSQL
    print_status "Starting test PostgreSQL container..."
    docker run -d \
        --name $TEST_POSTGRES_CONTAINER \
        -e POSTGRES_PASSWORD=test \
        -e POSTGRES_DB=$TEST_DB_NAME \
        -p 5433:5432 \
        postgres:14
    
    # Start test RabbitMQ
    print_status "Starting test RabbitMQ container..."
    docker run -d \
        --name $TEST_RABBITMQ_CONTAINER \
        -p 5673:5672 \
        -p 15673:15672 \
        rabbitmq:3-management
    
    # Wait for services to be ready
    print_status "Waiting for test services to be ready..."
    sleep 10
    
    # Test database connection
    until docker exec $TEST_POSTGRES_CONTAINER pg_isready -U postgres; do
        print_status "Waiting for PostgreSQL to be ready..."
        sleep 2
    done
    
    # Test RabbitMQ connection
    until docker exec $TEST_RABBITMQ_CONTAINER rabbitmq-diagnostics ping; do
        print_status "Waiting for RabbitMQ to be ready..."
        sleep 2
    done
    
    print_success "Test environment setup complete"
}

# Function to cleanup test environment
cleanup_test_environment() {
    print_status "Cleaning up test environment..."
    docker stop $TEST_POSTGRES_CONTAINER $TEST_RABBITMQ_CONTAINER 2>/dev/null || true
    docker rm $TEST_POSTGRES_CONTAINER $TEST_RABBITMQ_CONTAINER 2>/dev/null || true
    print_success "Test environment cleaned up"
}

# Function to run unit tests for a service
run_unit_tests() {
    local service=$1
    print_status "Running unit tests for $service..."
    
    cd $service
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies for $service..."
        npm install
    fi
    
    # Run unit tests
    if npm run test:unit 2>/dev/null; then
        print_success "Unit tests passed for $service"
    elif npm test 2>/dev/null; then
        print_success "Tests passed for $service"
    else
        print_error "Tests failed for $service"
        return 1
    fi
    
    cd ..
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running integration tests..."
    
    # Set test environment variables
    export NODE_ENV=test
    export DB_HOST=localhost
    export DB_PORT=5433
    export DB_NAME=$TEST_DB_NAME
    export DB_USER=postgres
    export DB_PASSWORD=test
    export RABBITMQ_URL=amqp://localhost:5673
    export JWT_SECRET=test_jwt_secret_key_for_testing_only
    export INTERNAL_SERVICE_KEY=test_internal_service_key_for_testing
    
    # Run integration tests if they exist
    if [ -f "tests/integration/run-integration-tests.js" ]; then
        node tests/integration/run-integration-tests.js
    else
        print_warning "No integration tests found"
    fi
}

# Function to run end-to-end tests
run_e2e_tests() {
    print_status "Running end-to-end tests..."
    
    # Start all services in background
    print_status "Starting all services for E2E testing..."
    
    for service in "${SERVICES[@]}"; do
        if [ -d "$service" ]; then
            print_status "Starting $service..."
            cd $service
            npm start &
            SERVICE_PIDS+=($!)
            cd ..
            sleep 2
        fi
    done
    
    # Wait for services to be ready
    sleep 10
    
    # Run E2E tests if they exist
    if [ -f "tests/e2e/run-e2e-tests.js" ]; then
        node tests/e2e/run-e2e-tests.js
    else
        print_warning "No E2E tests found"
    fi
    
    # Stop all services
    print_status "Stopping services..."
    for pid in "${SERVICE_PIDS[@]}"; do
        kill $pid 2>/dev/null || true
    done
}

# Function to run performance tests
run_performance_tests() {
    print_status "Running performance tests..."
    
    if command_exists autocannon; then
        # Run basic load test
        autocannon -c 10 -d 30 http://localhost:3000/health
    else
        print_warning "Autocannon not installed, skipping performance tests"
        print_status "Install with: npm install -g autocannon"
    fi
}

# Function to run security tests
run_security_tests() {
    print_status "Running security tests..."
    
    # Run npm audit for each service
    for service in "${SERVICES[@]}"; do
        if [ -d "$service" ]; then
            print_status "Running security audit for $service..."
            cd $service
            npm audit --audit-level=high || print_warning "Security vulnerabilities found in $service"
            cd ..
        fi
    done
}

# Function to generate test report
generate_test_report() {
    print_status "Generating test report..."
    
    local report_file="test-report-$(date +%Y%m%d-%H%M%S).html"
    
    cat > $report_file << EOF
<!DOCTYPE html>
<html>
<head>
    <title>ATMA Backend Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007cba; }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>ATMA Backend Test Report</h1>
        <p>Generated on: $(date)</p>
        <p>Test Environment: Test Containers</p>
    </div>
    
    <div class="section success">
        <h2>Test Summary</h2>
        <p>All critical tests have been executed. See details below.</p>
    </div>
    
    <div class="section">
        <h2>Services Tested</h2>
        <ul>
EOF

    for service in "${SERVICES[@]}"; do
        echo "            <li>$service</li>" >> $report_file
    done

    cat >> $report_file << EOF
        </ul>
    </div>
    
    <div class="section">
        <h2>Test Types Executed</h2>
        <ul>
            <li>Unit Tests</li>
            <li>Integration Tests</li>
            <li>End-to-End Tests</li>
            <li>Performance Tests</li>
            <li>Security Tests</li>
        </ul>
    </div>
</body>
</html>
EOF

    print_success "Test report generated: $report_file"
}

# Main function
main() {
    local test_type=${1:-"all"}
    
    print_status "Starting ATMA Backend Test Suite"
    print_status "Test type: $test_type"
    
    # Trap to cleanup on exit
    trap cleanup_test_environment EXIT
    
    case $test_type in
        "unit")
            for service in "${SERVICES[@]}"; do
                if [ -d "$service" ]; then
                    run_unit_tests $service
                fi
            done
            ;;
        "integration")
            setup_test_environment
            run_integration_tests
            ;;
        "e2e")
            setup_test_environment
            run_e2e_tests
            ;;
        "performance")
            setup_test_environment
            run_performance_tests
            ;;
        "security")
            run_security_tests
            ;;
        "all")
            # Run all tests
            setup_test_environment
            
            # Unit tests
            for service in "${SERVICES[@]}"; do
                if [ -d "$service" ]; then
                    run_unit_tests $service
                fi
            done
            
            # Integration tests
            run_integration_tests
            
            # E2E tests
            run_e2e_tests
            
            # Performance tests
            run_performance_tests
            
            # Security tests
            run_security_tests
            
            # Generate report
            generate_test_report
            ;;
        *)
            print_error "Unknown test type: $test_type"
            echo "Usage: $0 [unit|integration|e2e|performance|security|all]"
            exit 1
            ;;
    esac
    
    print_success "Test suite completed successfully!"
}

# Run main function with all arguments
main "$@"
