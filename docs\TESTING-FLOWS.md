# 🔄 ATMA Backend Testing Flows

Dokumentasi detail tentang flow testing yang mencakup semua skenario dan langkah-langkah testing untuk aplikasi ATMA Backend.

## 📋 Table of Contents

- [Testing Flow Overview](#testing-flow-overview)
- [User Registration & Authentication Flow](#user-registration--authentication-flow)
- [Assessment Submission Flow](#assessment-submission-flow)
- [Assessment Processing Flow](#assessment-processing-flow)
- [Result Retrieval Flow](#result-retrieval-flow)
- [Error Handling Flows](#error-handling-flows)
- [Performance Testing Flows](#performance-testing-flows)
- [Security Testing Flows](#security-testing-flows)

## 🎯 Testing Flow Overview

```mermaid
graph TD
    A[Start Testing] --> B[Unit Tests]
    B --> C[Integration Tests]
    C --> D[E2E Tests]
    D --> E[Performance Tests]
    E --> F[Security Tests]
    F --> G[Test Reports]

    B --> B1[API Gateway Tests]
    B --> B2[Auth Service Tests]
    B --> B3[Assessment Service Tests]
    B --> B4[Analysis Worker Tests]
    B --> B5[Archive Service Tests]

    C --> C1[Service Communication]
    C --> C2[Database Integration]
    C --> C3[RabbitMQ Integration]

    D --> D1[Complete User Journey]
    D --> D2[Error Scenarios]

    E --> E1[Load Testing]
    E --> E2[Stress Testing]

    F --> F1[Authentication Security]
    F --> F2[Input Validation]
    F --> F3[Rate Limiting]
```

## 👤 User Registration & Authentication Flow

### Flow 1: Successful User Registration

```javascript
describe('User Registration Flow', () => {
  it('should complete user registration successfully', async () => {
    // Step 1: Prepare test data
    const userData = {
      email: '<EMAIL>',
      password: 'SecurePass123!',
      fullName: 'New Test User'
    };

    // Step 2: Send registration request
    const response = await request(apiGateway)
      .post('/api/auth/register')
      .send(userData)
      .expect(201);

    // Step 3: Verify response structure
    expect(response.body).toMatchObject({
      success: true,
      data: {
        user: {
          id: expect.any(String),
          email: userData.email,
          fullName: userData.fullName,
          tokenBalance: 3 // Default token balance
        },
        token: expect.any(String)
      }
    });

    // Step 4: Verify JWT token is valid
    const decodedToken = jwt.verify(response.body.data.token, process.env.JWT_SECRET);
    expect(decodedToken).toMatchObject({
      userId: response.body.data.user.id,
      email: userData.email
    });

    // Step 5: Verify user is saved in database
    const savedUser = await User.findByPk(response.body.data.user.id);
    expect(savedUser).toBeTruthy();
    expect(savedUser.email).toBe(userData.email);
    expect(savedUser.tokenBalance).toBe(3);
  });
});
```

### Flow 2: User Login Process

```javascript
describe('User Login Flow', () => {
  let existingUser;

  beforeEach(async () => {
    // Setup: Create existing user
    existingUser = await createTestUser({
      email: '<EMAIL>',
      password: 'password123'
    });
  });

  it('should login user successfully', async () => {
    // Step 1: Send login request
    const response = await request(apiGateway)
      .post('/api/auth/login')
      .send({
        email: existingUser.email,
        password: 'password123'
      })
      .expect(200);

    // Step 2: Verify response
    expect(response.body).toMatchObject({
      success: true,
      data: {
        user: {
          id: existingUser.id,
          email: existingUser.email,
          tokenBalance: expect.any(Number)
        },
        token: expect.any(String)
      }
    });

    // Step 3: Verify token can be used for authenticated requests
    const protectedResponse = await request(apiGateway)
      .get('/api/archive/results')
      .set('Authorization', `Bearer ${response.body.data.token}`)
      .expect(200);

    expect(protectedResponse.body.success).toBe(true);
  });
});
```

## 📝 Assessment Submission Flow

### Flow 3: Complete Assessment Submission

```javascript
describe('Assessment Submission Flow', () => {
  let userToken;
  let assessmentData;

  beforeEach(async () => {
    // Setup: Create user and get token
    const user = await createTestUser();
    userToken = generateJWTToken(user);

    // Load test assessment data
    assessmentData = {
      riasec: {
        realistic: [4, 5, 3, 4, 5],
        investigative: [5, 4, 5, 3, 4],
        artistic: [3, 2, 4, 5, 3],
        social: [5, 5, 4, 5, 4],
        enterprising: [4, 3, 5, 4, 3],
        conventional: [2, 3, 2, 3, 4]
      },
      ocean: {
        openness: [4, 5, 3, 4, 5],
        conscientiousness: [5, 4, 5, 4, 5],
        extraversion: [3, 4, 3, 5, 4],
        agreeableness: [5, 5, 4, 5, 4],
        neuroticism: [2, 3, 2, 3, 2]
      },
      via_is: {
        wisdom: [4, 5, 4],
        courage: [5, 4, 5],
        humanity: [4, 5, 4],
        justice: [5, 5, 4],
        temperance: [4, 4, 5],
        transcendence: [5, 4, 4]
      },
      multiple_intelligences: {
        linguistic: [4, 5, 4],
        logical_mathematical: [5, 4, 5],
        spatial: [3, 4, 3],
        musical: [2, 3, 4],
        bodily_kinesthetic: [4, 3, 4],
        interpersonal: [5, 5, 4],
        intrapersonal: [4, 5, 5],
        naturalistic: [3, 4, 3]
      },
      cognitive_style: {
        knowing: [4, 5, 4],
        planning: [5, 4, 5],
        creating: [4, 3, 4]
      }
    };
  });

  it('should submit assessment successfully', async () => {
    // Step 1: Check initial token balance
    const initialBalance = await getUserTokenBalance(userToken);
    expect(initialBalance).toBeGreaterThanOrEqual(1);

    // Step 2: Submit assessment
    const response = await request(apiGateway)
      .post('/api/assessments/submit')
      .set('Authorization', `Bearer ${userToken}`)
      .send(assessmentData)
      .expect(202);

    // Step 3: Verify response structure
    expect(response.body).toMatchObject({
      success: true,
      data: {
        jobId: expect.any(String),
        message: 'Assessment submitted successfully',
        estimatedProcessingTime: '1-2 minutes'
      }
    });

    // Step 4: Verify token balance is deducted
    const newBalance = await getUserTokenBalance(userToken);
    expect(newBalance).toBe(initialBalance - 1);

    // Step 5: Verify job is created in database
    const analysisResult = await AnalysisResult.findOne({
      where: { jobId: response.body.data.jobId }
    });
    expect(analysisResult).toBeTruthy();
    expect(analysisResult.status).toBe('pending');

    // Step 6: Verify message is published to RabbitMQ
    const queueMessage = await getMessageFromQueue('assessment_analysis');
    expect(queueMessage).toBeTruthy();
    expect(JSON.parse(queueMessage.content)).toMatchObject({
      jobId: response.body.data.jobId,
      assessmentData: assessmentData
    });
  });
});
```

## ⚙️ Assessment Processing Flow

### Flow 4: Analysis Worker Processing

```javascript
describe('Assessment Processing Flow', () => {
  let mockAIResponse;

  beforeEach(() => {
    // Mock Google AI response
    mockAIResponse = {
      persona_profile: {
        riasec_scores: {
          realistic: 3.8,
          investigative: 4.2,
          artistic: 3.4,
          social: 4.6,
          enterprising: 3.8,
          conventional: 2.8
        },
        ocean_scores: {
          openness: 4.2,
          conscientiousness: 4.6,
          extraversion: 3.8,
          agreeableness: 4.6,
          neuroticism: 2.4
        },
        career_recommendations: [
          {
            title: "Software Developer",
            match_percentage: 85,
            reasoning: "High investigative and realistic scores"
          }
        ],
        personality_summary: "You are a thoughtful and analytical person..."
      }
    };

    // Mock Google AI API
    jest.spyOn(aiService, 'generatePersonaProfile')
      .mockResolvedValue(mockAIResponse);
  });

  it('should process assessment through complete pipeline', async () => {
    // Step 1: Create test job message
    const jobMessage = {
      jobId: 'test-job-123',
      userId: 'user-123',
      userEmail: '<EMAIL>',
      assessmentData: assessmentData,
      timestamp: new Date().toISOString(),
      retryCount: 0
    };

    // Step 2: Process message through worker
    const result = await assessmentProcessor.processAssessment(jobMessage);

    // Step 3: Verify AI service was called correctly
    expect(aiService.generatePersonaProfile).toHaveBeenCalledWith(
      expect.objectContaining({
        riasec: assessmentData.riasec,
        ocean: assessmentData.ocean
      })
    );

    // Step 4: Verify result structure
    expect(result).toMatchObject({
      id: expect.any(String),
      status: 'completed',
      persona_profile: mockAIResponse.persona_profile
    });

    // Step 5: Verify result is saved to archive service
    const savedResult = await request(archiveService)
      .get(`/internal/results/${result.id}`)
      .set('X-Service-Key', process.env.INTERNAL_SERVICE_KEY)
      .set('X-Internal-Service', 'true')
      .expect(200);

    expect(savedResult.body.data).toMatchObject({
      id: result.id,
      status: 'completed',
      persona_profile: mockAIResponse.persona_profile
    });

    // Step 6: Verify notification is sent (if notification service exists)
    // This would be tested when notification service is implemented
  });
});
```

### Flow 5: Error Handling in Processing

```javascript
describe('Processing Error Handling Flow', () => {
  it('should handle AI API failures with retry', async () => {
    // Step 1: Mock AI API to fail initially, then succeed
    jest.spyOn(aiService, 'generatePersonaProfile')
      .mockRejectedValueOnce(new Error('AI API temporarily unavailable'))
      .mockResolvedValueOnce(mockAIResponse);

    // Step 2: Create job message
    const jobMessage = {
      jobId: 'test-job-retry',
      userId: 'user-123',
      assessmentData: assessmentData,
      retryCount: 0
    };

    // Step 3: Process message (should retry automatically)
    const result = await assessmentProcessor.processAssessment(jobMessage);

    // Step 4: Verify retry occurred and eventually succeeded
    expect(aiService.generatePersonaProfile).toHaveBeenCalledTimes(2);
    expect(result.status).toBe('completed');
  });

  it('should send to DLQ after max retries', async () => {
    // Step 1: Mock AI API to always fail
    jest.spyOn(aiService, 'generatePersonaProfile')
      .mockRejectedValue(new Error('Persistent AI API failure'));

    // Step 2: Create job message with max retry count
    const jobMessage = {
      jobId: 'test-job-dlq',
      userId: 'user-123',
      assessmentData: assessmentData,
      retryCount: 3 // Max retries reached
    };

    // Step 3: Process message (should fail and go to DLQ)
    await expect(assessmentProcessor.processAssessment(jobMessage))
      .rejects.toThrow('Persistent AI API failure');

    // Step 4: Verify message goes to dead letter queue
    const dlqMessage = await getMessageFromQueue('assessment_analysis_dlq');
    expect(dlqMessage).toBeTruthy();
    expect(JSON.parse(dlqMessage.content).jobId).toBe('test-job-dlq');
  });
});
```

## 📊 Result Retrieval Flow

### Flow 6: Get User Results

```javascript
describe('Result Retrieval Flow', () => {
  let userToken;
  let completedResult;

  beforeEach(async () => {
    // Setup: Create user and completed result
    const user = await createTestUser();
    userToken = generateJWTToken(user);

    completedResult = await createCompletedResult({
      userId: user.id,
      persona_profile: mockAIResponse.persona_profile
    });
  });

  it('should retrieve user results successfully', async () => {
    // Step 1: Get all results for user
    const response = await request(apiGateway)
      .get('/api/archive/results')
      .set('Authorization', `Bearer ${userToken}`)
      .expect(200);

    // Step 2: Verify response structure
    expect(response.body).toMatchObject({
      success: true,
      data: expect.arrayContaining([
        expect.objectContaining({
          id: completedResult.id,
          status: 'completed',
          persona_profile: expect.any(Object)
        })
      ]),
      pagination: {
        page: 1,
        limit: 10,
        total: expect.any(Number)
      }
    });

    // Step 3: Get specific result by ID
    const specificResponse = await request(apiGateway)
      .get(`/api/archive/results/${completedResult.id}`)
      .set('Authorization', `Bearer ${userToken}`)
      .expect(200);

    // Step 4: Verify detailed result
    expect(specificResponse.body.data).toMatchObject({
      id: completedResult.id,
      status: 'completed',
      persona_profile: {
        riasec_scores: expect.any(Object),
        ocean_scores: expect.any(Object),
        career_recommendations: expect.any(Array)
      },
      createdAt: expect.any(String),
      updatedAt: expect.any(String)
    });
  });

  it('should handle pagination correctly', async () => {
    // Step 1: Create multiple results
    await Promise.all([
      createCompletedResult({ userId: getUserIdFromToken(userToken) }),
      createCompletedResult({ userId: getUserIdFromToken(userToken) }),
      createCompletedResult({ userId: getUserIdFromToken(userToken) })
    ]);

    // Step 2: Get first page
    const page1Response = await request(apiGateway)
      .get('/api/archive/results?page=1&limit=2')
      .set('Authorization', `Bearer ${userToken}`)
      .expect(200);

    expect(page1Response.body.data).toHaveLength(2);
    expect(page1Response.body.pagination.page).toBe(1);

    // Step 3: Get second page
    const page2Response = await request(apiGateway)
      .get('/api/archive/results?page=2&limit=2')
      .set('Authorization', `Bearer ${userToken}`)
      .expect(200);

    expect(page2Response.body.data.length).toBeGreaterThan(0);
    expect(page2Response.body.pagination.page).toBe(2);
  });
});
```

## ❌ Error Handling Flows

### Flow 7: Authentication Errors

```javascript
describe('Authentication Error Flows', () => {
  it('should handle missing token', async () => {
    const response = await request(apiGateway)
      .get('/api/archive/results')
      .expect(401);

    expect(response.body).toMatchObject({
      success: false,
      error: {
        code: 'MISSING_TOKEN',
        message: 'Authorization token is required'
      }
    });
  });

  it('should handle expired token', async () => {
    const expiredToken = generateExpiredJWTToken();

    const response = await request(apiGateway)
      .get('/api/archive/results')
      .set('Authorization', `Bearer ${expiredToken}`)
      .expect(401);

    expect(response.body.error.code).toBe('TOKEN_EXPIRED');
  });

  it('should handle invalid token', async () => {
    const invalidToken = 'invalid.jwt.token';

    const response = await request(apiGateway)
      .get('/api/archive/results')
      .set('Authorization', `Bearer ${invalidToken}`)
      .expect(401);

    expect(response.body.error.code).toBe('INVALID_TOKEN');
  });
});
```

### Flow 8: Validation Errors

```javascript
describe('Validation Error Flows', () => {
  let userToken;

  beforeEach(async () => {
    const user = await createTestUser();
    userToken = generateJWTToken(user);
  });

  it('should handle invalid assessment data', async () => {
    const invalidData = {
      riasec: {
        realistic: [1, 2] // Too few answers
      }
    };

    const response = await request(apiGateway)
      .post('/api/assessments/submit')
      .set('Authorization', `Bearer ${userToken}`)
      .send(invalidData)
      .expect(400);

    expect(response.body).toMatchObject({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: expect.stringContaining('riasec.realistic'),
        details: expect.any(Array)
      }
    });
  });

  it('should handle missing required fields', async () => {
    const incompleteData = {
      riasec: assessmentData.riasec
      // Missing other required fields
    };

    const response = await request(apiGateway)
      .post('/api/assessments/submit')
      .set('Authorization', `Bearer ${userToken}`)
      .send(incompleteData)
      .expect(400);

    expect(response.body.error.code).toBe('VALIDATION_ERROR');
    expect(response.body.error.details).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          field: 'ocean',
          message: expect.any(String)
        })
      ])
    );
  });
});
```

### Flow 9: Service Unavailability

```javascript
describe('Service Unavailability Flows', () => {
  it('should handle auth service downtime', async () => {
    // Mock auth service to be unavailable
    jest.spyOn(axios, 'get').mockRejectedValue(
      new Error('connect ECONNREFUSED 127.0.0.1:3001')
    );

    const response = await request(apiGateway)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password'
      })
      .expect(503);

    expect(response.body).toMatchObject({
      success: false,
      error: {
        code: 'SERVICE_UNAVAILABLE',
        message: 'Auth service is currently unavailable'
      }
    });
  });

  it('should handle RabbitMQ connection failure', async () => {
    // Mock RabbitMQ connection failure
    jest.spyOn(rabbitmq, 'getChannel').mockRejectedValue(
      new Error('Connection to RabbitMQ failed')
    );

    const response = await request(apiGateway)
      .post('/api/assessments/submit')
      .set('Authorization', `Bearer ${userToken}`)
      .send(assessmentData)
      .expect(503);

    expect(response.body.error.code).toBe('QUEUE_UNAVAILABLE');
  });
});
```

## ⚡ Performance Testing Flows

### Flow 10: Load Testing Scenarios

```javascript
describe('Performance Testing Flows', () => {
  describe('Concurrent User Load', () => {
    it('should handle 100 concurrent login requests', async () => {
      const users = await createMultipleTestUsers(100);

      const loginPromises = users.map(user =>
        request(apiGateway)
          .post('/api/auth/login')
          .send({
            email: user.email,
            password: 'password123'
          })
      );

      const startTime = Date.now();
      const results = await Promise.all(loginPromises);
      const endTime = Date.now();

      // All requests should succeed
      results.forEach(result => {
        expect(result.status).toBe(200);
        expect(result.body.success).toBe(true);
      });

      // Performance assertion
      const totalTime = endTime - startTime;
      const avgResponseTime = totalTime / results.length;
      expect(avgResponseTime).toBeLessThan(500); // Average < 500ms
    });
  });

  describe('Assessment Processing Load', () => {
    it('should process multiple assessments concurrently', async () => {
      const userTokens = await createMultipleUsersWithTokens(50);

      const submissionPromises = userTokens.map(token =>
        request(apiGateway)
          .post('/api/assessments/submit')
          .set('Authorization', `Bearer ${token}`)
          .send(assessmentData)
      );

      const results = await Promise.all(submissionPromises);

      // All submissions should succeed
      results.forEach(result => {
        expect(result.status).toBe(202);
        expect(result.body.data).toHaveProperty('jobId');
      });

      // Wait for processing to complete
      const jobIds = results.map(r => r.body.data.jobId);
      await waitForAllJobsToComplete(jobIds, 300000); // 5 minutes timeout

      // Verify all jobs completed successfully
      for (const jobId of jobIds) {
        const result = await getAnalysisResult(jobId);
        expect(result.status).toBe('completed');
      }
    });
  });
});
```

## 🔒 Security Testing Flows

### Flow 11: Security Vulnerability Tests

```javascript
describe('Security Testing Flows', () => {
  describe('SQL Injection Prevention', () => {
    it('should prevent SQL injection in login', async () => {
      const maliciousInputs = [
        "admin'; DROP TABLE users; --",
        "' OR '1'='1",
        "'; UPDATE users SET password='hacked' WHERE email='<EMAIL>'; --"
      ];

      for (const maliciousInput of maliciousInputs) {
        const response = await request(apiGateway)
          .post('/api/auth/login')
          .send({
            email: maliciousInput,
            password: 'password'
          })
          .expect(400);

        expect(response.body.error.code).toBe('VALIDATION_ERROR');
      }

      // Verify database integrity
      const userCount = await User.count();
      expect(userCount).toBeGreaterThan(0); // Users table should still exist
    });
  });

  describe('XSS Prevention', () => {
    it('should sanitize user inputs', async () => {
      const xssPayloads = [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("xss")'
      ];

      for (const payload of xssPayloads) {
        const response = await request(apiGateway)
          .post('/api/auth/register')
          .send({
            email: '<EMAIL>',
            password: 'password123',
            fullName: payload
          })
          .expect(400);

        expect(response.body.error.code).toBe('VALIDATION_ERROR');
      }
    });
  });

  describe('Rate Limiting Security', () => {
    it('should prevent brute force attacks', async () => {
      const attempts = [];

      // Make 15 failed login attempts
      for (let i = 0; i < 15; i++) {
        attempts.push(
          request(apiGateway)
            .post('/api/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'wrongpassword'
            })
        );
      }

      const results = await Promise.all(attempts);

      // First few attempts should return 401 (unauthorized)
      expect(results.slice(0, 5).every(r => r.status === 401)).toBe(true);

      // Later attempts should be rate limited (429)
      expect(results.slice(-5).every(r => r.status === 429)).toBe(true);
    });
  });

  describe('Authorization Security', () => {
    it('should prevent access to other users data', async () => {
      // Create two users
      const user1 = await createTestUser({ email: '<EMAIL>' });
      const user2 = await createTestUser({ email: '<EMAIL>' });

      const token1 = generateJWTToken(user1);
      const token2 = generateJWTToken(user2);

      // Create result for user1
      const result1 = await createCompletedResult({ userId: user1.id });

      // User2 tries to access user1's result
      const response = await request(apiGateway)
        .get(`/api/archive/results/${result1.id}`)
        .set('Authorization', `Bearer ${token2}`)
        .expect(403);

      expect(response.body.error.code).toBe('FORBIDDEN');
    });
  });
});
```

---

**Dokumentasi ini memberikan panduan detail tentang semua flow testing yang perlu dilakukan untuk memastikan aplikasi ATMA Backend berfungsi dengan baik, aman, dan performant!** 🚀