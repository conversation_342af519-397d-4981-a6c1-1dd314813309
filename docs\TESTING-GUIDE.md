# 🧪 ATMA Backend Testing Guide

Panduan lengkap untuk testing aplikasi ATMA Backend yang mencakup semua aspek dari unit testing hingga end-to-end testing.

## 📋 Table of Contents

- [Overview](#overview)
- [Testing Strategy](#testing-strategy)
- [Setup Testing Environment](#setup-testing-environment)
- [Unit Testing](#unit-testing)
- [Integration Testing](#integration-testing)
- [End-to-End Testing](#end-to-end-testing)
- [Performance Testing](#performance-testing)
- [Security Testing](#security-testing)
- [Test Automation](#test-automation)

## 🎯 Overview

ATMA Backend menggunakan microservices architecture dengan komponen:
- **API Gateway** - Entry point dan routing
- **Auth Service** - Authentication dan authorization
- **Assessment Service** - Menerima dan queue assessment data
- **Analysis Worker** - Background processing dengan AI
- **Archive Service** - Storage hasil analisis

## 🏗️ Testing Strategy

### Testing Pyramid

```
    /\     E2E Tests (10%)
   /  \    - Complete user journeys
  /____\   - Critical business flows
 /      \  
/________\  Integration Tests (20%)
           - Service communication
           - Database integration
           - RabbitMQ integration

Unit Tests (70%)
- Business logic
- Controllers
- Services
- Utilities
```

### Test Types Coverage

| Test Type | Coverage | Tools | Purpose |
|-----------|----------|-------|---------|
| Unit | 80%+ | Jest, Sinon | Individual functions/modules |
| Integration | 100% paths | Supertest, Testcontainers | Service interactions |
| E2E | Critical flows | Playwright, Docker | Complete user journeys |
| Performance | Load scenarios | Autocannon, K6 | Scalability validation |
| Security | Vulnerabilities | OWASP ZAP, Snyk | Security assessment |

## 🛠️ Setup Testing Environment

### Prerequisites

```bash
# Install dependencies
npm install

# Setup test databases
docker run --name test-postgres -e POSTGRES_PASSWORD=test -p 5433:5432 -d postgres:14
docker run --name test-rabbitmq -p 5673:5672 -d rabbitmq:3-management

# Environment variables for testing
cp .env.example .env.test
```

### Test Environment Configuration

```env
# .env.test
NODE_ENV=test
DB_HOST=localhost
DB_PORT=5433
DB_NAME=atma_test_db
DB_USER=postgres
DB_PASSWORD=test
RABBITMQ_URL=amqp://localhost:5673
JWT_SECRET=test_jwt_secret_key_for_testing_only
INTERNAL_SERVICE_KEY=test_internal_service_key_for_testing
```

## 🔬 Unit Testing

### 1. API Gateway Unit Tests

#### Test Structure
```
api-gateway/tests/
├── app.test.js                 # Main application tests
├── middleware/
│   ├── auth.test.js           # Authentication middleware
│   ├── rateLimiter.test.js    # Rate limiting
│   └── proxy.test.js          # Proxy functionality
├── routes/
│   └── health.test.js         # Health check endpoints
└── utils/
    └── logger.test.js         # Logging utilities
```

#### Key Test Scenarios

**Authentication Middleware Tests:**
```javascript
describe('Auth Middleware', () => {
  it('should validate JWT token correctly');
  it('should reject expired tokens');
  it('should reject malformed tokens');
  it('should pass valid tokens to next middleware');
  it('should handle missing authorization header');
});
```

**Rate Limiter Tests:**
```javascript
describe('Rate Limiter', () => {
  it('should allow requests within limit');
  it('should block requests exceeding limit');
  it('should reset counter after time window');
  it('should apply different limits per endpoint');
});
```

**Proxy Tests:**
```javascript
describe('Proxy Middleware', () => {
  it('should route requests to correct service');
  it('should add internal service headers');
  it('should handle service unavailable errors');
  it('should timeout on slow responses');
});
```

### 2. Auth Service Unit Tests

#### Test Structure
```
auth-service/tests/
├── controllers/
│   └── auth.test.js           # Authentication controller
├── models/
│   └── user.test.js           # User model
├── services/
│   └── tokenService.test.js   # JWT token service
└── middleware/
    └── validation.test.js     # Input validation
```

#### Key Test Scenarios

**User Registration Tests:**
```javascript
describe('User Registration', () => {
  it('should register user with valid data');
  it('should hash password before saving');
  it('should generate JWT token');
  it('should set default token balance');
  it('should reject duplicate email');
  it('should validate email format');
  it('should validate password strength');
});
```

**User Login Tests:**
```javascript
describe('User Login', () => {
  it('should login with valid credentials');
  it('should reject invalid password');
  it('should reject non-existent user');
  it('should return user data and token');
  it('should update last login timestamp');
});
```

### 3. Assessment Service Unit Tests

#### Test Structure
```
assessment-service/tests/
├── controllers/
│   └── assessment.test.js     # Assessment controller
├── services/
│   ├── queueService.test.js   # RabbitMQ service
│   └── validation.test.js     # Assessment validation
└── utils/
    └── assessmentParser.test.js # Data parsing
```

#### Key Test Scenarios

**Assessment Submission Tests:**
```javascript
describe('Assessment Submission', () => {
  it('should validate assessment data structure');
  it('should check user token balance');
  it('should deduct tokens after submission');
  it('should publish job to RabbitMQ');
  it('should return job ID');
  it('should handle insufficient tokens');
  it('should validate RIASEC data format');
  it('should validate OCEAN data format');
});
```

**Queue Service Tests:**
```javascript
describe('Queue Service', () => {
  it('should publish message to correct queue');
  it('should include job metadata');
  it('should handle RabbitMQ connection errors');
  it('should retry failed publications');
  it('should generate unique job IDs');
});
```

### 4. Analysis Worker Unit Tests

#### Test Structure
```
analysis-worker/tests/
├── processors/
│   └── assessmentProcessor.test.js # Main processor
├── services/
│   ├── queueConsumer.test.js      # RabbitMQ consumer
│   ├── aiService.test.js          # Google AI integration
│   └── archiveService.test.js     # Archive service client
└── utils/
    ├── promptBuilder.test.js      # AI prompt construction
    └── validator.test.js          # Data validation
```

#### Key Test Scenarios

**Assessment Processing Tests:**
```javascript
describe('Assessment Processor', () => {
  it('should process RIASEC assessment data');
  it('should process OCEAN assessment data');
  it('should build correct AI prompt');
  it('should call Google AI API');
  it('should parse AI response');
  it('should save results to archive');
  it('should handle AI API errors');
  it('should validate response format');
});
```

**Queue Consumer Tests:**
```javascript
describe('Queue Consumer', () => {
  it('should consume messages from queue');
  it('should acknowledge successful processing');
  it('should retry failed messages');
  it('should send to DLQ after max retries');
  it('should handle malformed messages');
  it('should respect concurrency limits');
});
```

### 5. Archive Service Unit Tests

#### Test Structure
```
archive-service/tests/
├── controllers/
│   └── results.test.js        # Results controller
├── models/
│   └── analysisResult.test.js # Analysis result model
├── services/
│   └── resultService.test.js  # Business logic
└── middleware/
    └── auth.test.js           # Service authentication
```

#### Key Test Scenarios

**Results Management Tests:**
```javascript
describe('Results Controller', () => {
  it('should create new analysis result');
  it('should get results by user ID');
  it('should get result by ID');
  it('should update result status');
  it('should handle pagination');
  it('should filter by status');
  it('should validate user ownership');
});
```

## 🔗 Integration Testing

### 1. Service Communication Tests

#### API Gateway ↔ Services Integration
```javascript
describe('API Gateway Integration', () => {
  beforeAll(async () => {
    // Start all services
    await startTestServices();
  });

  describe('Auth Service Integration', () => {
    it('should proxy login requests to auth service');
    it('should proxy registration requests');
    it('should handle auth service downtime');
    it('should pass through authentication headers');
  });

  describe('Assessment Service Integration', () => {
    it('should proxy assessment submissions');
    it('should validate JWT before proxying');
    it('should handle assessment service errors');
  });
});
```

#### Assessment Service ↔ RabbitMQ Integration
```javascript
describe('Assessment-RabbitMQ Integration', () => {
  let rabbitConnection;

  beforeAll(async () => {
    rabbitConnection = await setupTestRabbitMQ();
  });

  it('should publish assessment jobs to queue');
  it('should handle RabbitMQ connection failures');
  it('should retry failed publications');
  it('should create proper message structure');
});
```

#### Analysis Worker ↔ Archive Service Integration
```javascript
describe('Worker-Archive Integration', () => {
  it('should save analysis results to archive');
  it('should authenticate with service key');
  it('should handle archive service errors');
  it('should retry failed saves');
  it('should update result status correctly');
});
```

### 2. Database Integration Tests

```javascript
describe('Database Integration', () => {
  beforeEach(async () => {
    await cleanDatabase();
    await seedTestData();
  });

  describe('Auth Service Database', () => {
    it('should perform user CRUD operations');
    it('should handle unique constraints');
    it('should maintain referential integrity');
  });

  describe('Archive Service Database', () => {
    it('should store analysis results');
    it('should query results efficiently');
    it('should handle JSONB operations');
  });
});
```

## 🚀 End-to-End Testing

### Complete Assessment Flow

```javascript
describe('Complete Assessment Flow E2E', () => {
  let userToken;
  let assessmentData;

  beforeAll(async () => {
    // Setup test data
    assessmentData = loadTestAssessmentData();
  });

  it('should complete full assessment journey', async () => {
    // Step 1: User Registration
    const registerResponse = await request(apiGateway)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'SecurePass123!',
        fullName: 'Test User'
      })
      .expect(201);

    expect(registerResponse.body).toHaveProperty('token');
    userToken = registerResponse.body.token;

    // Step 2: Submit Assessment
    const assessmentResponse = await request(apiGateway)
      .post('/api/assessments/submit')
      .set('Authorization', `Bearer ${userToken}`)
      .send(assessmentData)
      .expect(202);

    expect(assessmentResponse.body).toHaveProperty('jobId');
    const jobId = assessmentResponse.body.jobId;

    // Step 3: Wait for Processing (with timeout)
    await waitForProcessingComplete(jobId, 120000); // 2 minutes

    // Step 4: Retrieve Results
    const resultsResponse = await request(apiGateway)
      .get('/api/archive/results')
      .set('Authorization', `Bearer ${userToken}`)
      .expect(200);

    // Step 5: Verify Results Structure
    expect(resultsResponse.body.data).toHaveLength(1);
    const result = resultsResponse.body.data[0];
    
    expect(result).toHaveProperty('id');
    expect(result).toHaveProperty('status', 'completed');
    expect(result).toHaveProperty('persona_profile');
    expect(result.persona_profile).toHaveProperty('riasec_scores');
    expect(result.persona_profile).toHaveProperty('ocean_scores');
    expect(result.persona_profile).toHaveProperty('career_recommendations');
  });
});
```

### Error Scenarios E2E

```javascript
describe('Error Scenarios E2E', () => {
  it('should handle insufficient token balance', async () => {
    // Create user with 0 tokens
    const user = await createUserWithTokens(0);
    
    const response = await request(apiGateway)
      .post('/api/assessments/submit')
      .set('Authorization', `Bearer ${user.token}`)
      .send(assessmentData)
      .expect(400);

    expect(response.body.error.code).toBe('INSUFFICIENT_TOKENS');
  });

  it('should handle invalid assessment data', async () => {
    const invalidData = { invalid: 'data' };
    
    const response = await request(apiGateway)
      .post('/api/assessments/submit')
      .set('Authorization', `Bearer ${userToken}`)
      .send(invalidData)
      .expect(400);

    expect(response.body.error.code).toBe('VALIDATION_ERROR');
  });

  it('should handle service downtime gracefully', async () => {
    // Stop assessment service
    await stopService('assessment-service');
    
    const response = await request(apiGateway)
      .post('/api/assessments/submit')
      .set('Authorization', `Bearer ${userToken}`)
      .send(assessmentData)
      .expect(503);

    expect(response.body.error.code).toBe('SERVICE_UNAVAILABLE');
  });
});
```

## ⚡ Performance Testing

### Load Testing Scenarios

```javascript
describe('Performance Tests', () => {
  describe('API Gateway Load Test', () => {
    it('should handle 100 concurrent users', async () => {
      const result = await autocannon({
        url: 'http://localhost:3000',
        connections: 100,
        duration: 60,
        requests: [
          {
            method: 'POST',
            path: '/api/auth/login',
            body: JSON.stringify(loginData),
            headers: { 'Content-Type': 'application/json' }
          }
        ]
      });

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.latency.p95).toBeLessThan(500); // 95th percentile < 500ms
    });
  });

  describe('Assessment Processing Load', () => {
    it('should process 50 assessments concurrently', async () => {
      const promises = Array(50).fill().map(() => 
        submitAssessment(userToken, assessmentData)
      );

      const results = await Promise.all(promises);
      
      // All should succeed
      results.forEach(result => {
        expect(result.status).toBe(202);
        expect(result.body).toHaveProperty('jobId');
      });

      // Wait for all to complete
      await waitForAllProcessingComplete(results.map(r => r.body.jobId));
    });
  });
});
```

### RabbitMQ Performance Tests

```javascript
describe('RabbitMQ Performance', () => {
  it('should handle message throughput', async () => {
    const messageCount = 1000;
    const startTime = Date.now();

    // Publish messages
    for (let i = 0; i < messageCount; i++) {
      await publishTestMessage(assessmentData);
    }

    // Wait for all to be consumed
    await waitForQueueEmpty();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    const throughput = messageCount / (duration / 1000); // messages per second

    expect(throughput).toBeGreaterThan(10); // At least 10 messages/second
  });
});
```

## 🔒 Security Testing

### Authentication & Authorization Tests

```javascript
describe('Security Tests', () => {
  describe('JWT Security', () => {
    it('should reject expired tokens', async () => {
      const expiredToken = generateExpiredToken();
      
      const response = await request(apiGateway)
        .get('/api/archive/results')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expect(response.body.error.code).toBe('TOKEN_EXPIRED');
    });

    it('should reject tampered tokens', async () => {
      const tamperedToken = userToken + 'tampered';
      
      const response = await request(apiGateway)
        .get('/api/archive/results')
        .set('Authorization', `Bearer ${tamperedToken}`)
        .expect(401);

      expect(response.body.error.code).toBe('INVALID_TOKEN');
    });
  });

  describe('Input Validation Security', () => {
    it('should prevent SQL injection', async () => {
      const maliciousInput = "'; DROP TABLE users; --";
      
      const response = await request(apiGateway)
        .post('/api/auth/login')
        .send({
          email: maliciousInput,
          password: 'password'
        })
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should prevent XSS attacks', async () => {
      const xssPayload = '<script>alert("xss")</script>';
      
      const response = await request(apiGateway)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'password',
          fullName: xssPayload
        })
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('Rate Limiting Security', () => {
    it('should block brute force attacks', async () => {
      const attempts = Array(10).fill().map(() =>
        request(apiGateway)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'wrongpassword'
          })
      );

      const results = await Promise.all(attempts);
      
      // Last attempts should be rate limited
      const rateLimited = results.slice(-3);
      rateLimited.forEach(result => {
        expect(result.status).toBe(429);
      });
    });
  });
});
```

### Internal Service Security Tests

```javascript
describe('Internal Service Security', () => {
  it('should require service key for internal endpoints', async () => {
    const response = await request(archiveService)
      .post('/internal/results')
      .send(resultData)
      .expect(401);

    expect(response.body.error.code).toBe('MISSING_SERVICE_KEY');
  });

  it('should validate service key correctly', async () => {
    const response = await request(archiveService)
      .post('/internal/results')
      .set('X-Service-Key', 'invalid_key')
      .send(resultData)
      .expect(401);

    expect(response.body.error.code).toBe('INVALID_SERVICE_KEY');
  });

  it('should accept valid service key', async () => {
    const response = await request(archiveService)
      .post('/internal/results')
      .set('X-Service-Key', process.env.INTERNAL_SERVICE_KEY)
      .set('X-Internal-Service', 'true')
      .send(resultData)
      .expect(201);

    expect(response.body).toHaveProperty('id');
  });
});
```

## 🤖 Test Automation

### Running Tests

```bash
# Run all tests
npm run test

# Run specific test types
npm run test:unit
npm run test:integration
npm run test:e2e
npm run test:performance
npm run test:security

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run tests for specific service
cd api-gateway && npm test
cd auth-service && npm test
cd assessment-service && npm test
cd analysis-worker && npm test
cd archive-service && npm test
```

### CI/CD Pipeline

```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [api-gateway, auth-service, assessment-service, analysis-worker, archive-service]
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: cd ${{ matrix.service }} && npm ci
      - name: Run unit tests
        run: cd ${{ matrix.service }} && npm run test:unit
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      rabbitmq:
        image: rabbitmq:3-management
        ports:
          - 5672:5672
    steps:
      - uses: actions/checkout@v3
      - name: Setup test environment
        run: |
          cp .env.example .env.test
          npm run setup:test-db
      - name: Run integration tests
        run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Start services
        run: docker-compose -f docker-compose.test.yml up -d
      - name: Wait for services
        run: ./scripts/wait-for-services.sh
      - name: Run E2E tests
        run: npm run test:e2e
      - name: Cleanup
        run: docker-compose -f docker-compose.test.yml down
```

### Test Reporting

```javascript
// jest.config.js
module.exports = {
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './test-reports',
      filename: 'test-report.html',
      expand: true
    }],
    ['jest-junit', {
      outputDirectory: './test-reports',
      outputName: 'junit.xml'
    }]
  ],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.test.js'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

## 📊 Test Metrics & Quality Gates

### Coverage Requirements
- **Unit Tests**: 80% minimum coverage
- **Integration Tests**: 100% service communication paths
- **E2E Tests**: 100% critical user journeys

### Performance Benchmarks
- **API Response Time**: < 200ms (95th percentile)
- **Assessment Processing**: < 2 minutes
- **Concurrent Users**: 100+ without degradation
- **Message Throughput**: 10+ messages/second

### Quality Gates
- All tests must pass before deployment
- No critical security vulnerabilities
- Performance benchmarks must be met
- Code coverage thresholds must be achieved

## 🔧 Troubleshooting

### Common Issues

**RabbitMQ Connection Issues:**
```bash
# Check RabbitMQ status
docker ps | grep rabbitmq
docker logs test-rabbitmq

# Reset RabbitMQ
docker restart test-rabbitmq
```

**Database Connection Issues:**
```bash
# Check PostgreSQL status
docker ps | grep postgres
docker logs test-postgres

# Reset database
npm run setup:test-db
```

**Test Timeouts:**
```javascript
// Increase timeout for slow tests
describe('Slow tests', () => {
  jest.setTimeout(30000); // 30 seconds
  
  it('should handle long processing', async () => {
    // Test implementation
  });
});
```

### Debug Mode

```bash
# Run tests in debug mode
DEBUG=* npm test

# Run specific test file
npm test -- --testPathPattern=auth.test.js

# Run tests with verbose output
npm test -- --verbose
```

---

**Dokumentasi ini memberikan panduan lengkap untuk testing aplikasi ATMA Backend dengan coverage yang komprehensif untuk semua aspek aplikasi!** 🚀
