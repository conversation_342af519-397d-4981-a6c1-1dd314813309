// Jest Configuration for Local Testing (No Docker)
// This configuration uses mocked external dependencies

module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // Global setup for mocking
  setupFilesAfterEnv: ['<rootDir>/tests/jest.setup.local.js'],
  
  // Test patterns
  testMatch: [
    '<rootDir>/**/*.test.js',
    '<rootDir>/tests/unit/**/*.test.js'
  ],
  
  // Ignore integration and e2e tests that require real services
  testPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
    '/test-reports/',
    '/tests/integration/',
    '/tests/e2e/'
  ],
  
  // Module mocking
  moduleNameMapping: {
    '^pg$': '<rootDir>/tests/mocks/pg.js',
    '^amqplib$': '<rootDir>/tests/mocks/amqplib.js',
    '^@google/generative-ai$': '<rootDir>/tests/mocks/google-ai.js'
  },
  
  // Coverage settings
  collectCoverage: true,
  collectCoverageFrom: [
    'api-gateway/src/**/*.js',
    'auth-service/src/**/*.js',
    'assessment-service/src/**/*.js',
    'analysis-worker/src/**/*.js',
    'archive-service/src/**/*.js',
    '!**/node_modules/**',
    '!**/tests/**',
    '!**/coverage/**'
  ],
  
  // Coverage thresholds (relaxed for local testing)
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // Test timeout
  testTimeout: 10000,
  
  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
  
  // Verbose output
  verbose: true,
  
  // Transform configuration
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // Environment variables for local testing
  testEnvironmentOptions: {
    NODE_ENV: 'test'
  }
};
