// Jest Configuration for ATMA Backend
// Root configuration for running tests across all microservices

module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // Projects configuration for multi-service testing
  projects: [
    {
      displayName: 'api-gateway',
      testMatch: ['<rootDir>/api-gateway/tests/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/api-gateway/tests/setup.js'],
      collectCoverageFrom: [
        '<rootDir>/api-gateway/src/**/*.js',
        '!<rootDir>/api-gateway/src/**/*.test.js',
        '!<rootDir>/api-gateway/src/app.js'
      ],
      coverageDirectory: '<rootDir>/coverage/api-gateway'
    },
    {
      displayName: 'auth-service',
      testMatch: ['<rootDir>/auth-service/tests/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/auth-service/tests/setup.js'],
      collectCoverageFrom: [
        '<rootDir>/auth-service/src/**/*.js',
        '!<rootDir>/auth-service/src/**/*.test.js',
        '!<rootDir>/auth-service/src/app.js'
      ],
      coverageDirectory: '<rootDir>/coverage/auth-service'
    },
    {
      displayName: 'assessment-service',
      testMatch: ['<rootDir>/assessment-service/tests/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/assessment-service/tests/setup.js'],
      collectCoverageFrom: [
        '<rootDir>/assessment-service/src/**/*.js',
        '!<rootDir>/assessment-service/src/**/*.test.js',
        '!<rootDir>/assessment-service/src/app.js'
      ],
      coverageDirectory: '<rootDir>/coverage/assessment-service'
    },
    {
      displayName: 'analysis-worker',
      testMatch: ['<rootDir>/analysis-worker/tests/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/analysis-worker/tests/setup.js'],
      collectCoverageFrom: [
        '<rootDir>/analysis-worker/src/**/*.js',
        '!<rootDir>/analysis-worker/src/**/*.test.js',
        '!<rootDir>/analysis-worker/src/worker.js'
      ],
      coverageDirectory: '<rootDir>/coverage/analysis-worker'
    },
    {
      displayName: 'archive-service',
      testMatch: ['<rootDir>/archive-service/tests/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/archive-service/tests/setup.js'],
      collectCoverageFrom: [
        '<rootDir>/archive-service/src/**/*.js',
        '!<rootDir>/archive-service/src/**/*.test.js',
        '!<rootDir>/archive-service/src/app.js'
      ],
      coverageDirectory: '<rootDir>/coverage/archive-service'
    },
    {
      displayName: 'integration',
      testMatch: ['<rootDir>/tests/integration/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/tests/integration/setup.js'],
      testTimeout: 30000 // 30 seconds for integration tests
    },
    {
      displayName: 'e2e',
      testMatch: ['<rootDir>/tests/e2e/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/tests/e2e/setup.js'],
      testTimeout: 120000 // 2 minutes for E2E tests
    }
  ],
  
  // Global coverage settings
  collectCoverage: true,
  coverageDirectory: '<rootDir>/coverage',
  coverageReporters: [
    'text',
    'text-summary',
    'lcov',
    'html',
    'json'
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    // Service-specific thresholds
    './api-gateway/src/**/*.js': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    },
    './auth-service/src/**/*.js': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './assessment-service/src/**/*.js': {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75
    },
    './analysis-worker/src/**/*.js': {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    },
    './archive-service/src/**/*.js': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  
  // Test reporters
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './test-reports',
      filename: 'test-report.html',
      expand: true,
      hideIcon: false,
      pageTitle: 'ATMA Backend Test Report',
      logoImgPath: undefined,
      includeFailureMsg: true,
      includeSuiteFailure: true
    }],
    ['jest-junit', {
      outputDirectory: './test-reports',
      outputName: 'junit.xml',
      ancestorSeparator: ' › ',
      uniqueOutputName: false,
      suiteNameTemplate: '{displayName} - {filepath}',
      classNameTemplate: '{classname}',
      titleTemplate: '{title}'
    }]
  ],
  
  // Global test setup
  globalSetup: '<rootDir>/tests/global-setup.js',
  globalTeardown: '<rootDir>/tests/global-teardown.js',
  
  // Module paths
  moduleDirectories: ['node_modules', '<rootDir>'],
  
  // Test patterns
  testPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
    '/test-reports/'
  ],
  
  // Transform configuration
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // Module file extensions
  moduleFileExtensions: ['js', 'json'],
  
  // Verbose output
  verbose: true,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true,
  
  // Force exit after tests complete
  forceExit: true,
  
  // Detect open handles
  detectOpenHandles: true,
  
  // Maximum worker processes
  maxWorkers: '50%',
  
  // Test timeout (default)
  testTimeout: 10000,
  
  // Setup files
  setupFiles: ['<rootDir>/tests/jest.setup.js'],
  
  // Environment variables for testing
  testEnvironmentOptions: {
    NODE_ENV: 'test'
  }
};
