// Mock AMQP (RabbitMQ) module for testing without message queue

const mockPublish = jest.fn();
const mockConsume = jest.fn();
const mockAck = jest.fn();
const mockNack = jest.fn();
const mockAssertExchange = jest.fn();
const mockAssertQueue = jest.fn();
const mockBindQueue = jest.fn();
const mockPrefetch = jest.fn();
const mockClose = jest.fn();

// Mock channel
const mockChannel = {
  publish: mockPublish,
  consume: mockConsume,
  ack: mockAck,
  nack: mockNack,
  assertExchange: mockAssertExchange,
  assertQueue: mockAssertQueue,
  bindQueue: mockBindQueue,
  prefetch: mockPrefetch,
  close: mockClose
};

// Mock connection
const mockConnection = {
  createChannel: jest.fn(() => Promise.resolve(mockChannel)),
  close: mockClose
};

// Mock connect function
const connect = jest.fn(() => Promise.resolve(mockConnection));

// Message queue for testing
let messageQueue = [];

// Setup default mock behavior
mockPublish.mockImplementation((exchange, routingKey, content, options) => {
  const message = {
    exchange,
    routingKey,
    content: content.toString(),
    options,
    timestamp: Date.now()
  };
  messageQueue.push(message);
  return true;
});

mockConsume.mockImplementation((queue, callback, options) => {
  // Simulate consuming messages from queue
  setImmediate(() => {
    messageQueue.forEach((message, index) => {
      const mockMessage = {
        content: Buffer.from(message.content),
        fields: {
          deliveryTag: index + 1,
          exchange: message.exchange,
          routingKey: message.routingKey
        },
        properties: message.options || {}
      };
      callback(mockMessage);
    });
  });
  return Promise.resolve({ consumerTag: 'mock-consumer-tag' });
});

mockAssertExchange.mockResolvedValue({ exchange: 'mock-exchange' });
mockAssertQueue.mockResolvedValue({ 
  queue: 'mock-queue',
  messageCount: messageQueue.length,
  consumerCount: 0
});
mockBindQueue.mockResolvedValue({});
mockPrefetch.mockResolvedValue({});
mockAck.mockImplementation(() => true);
mockNack.mockImplementation(() => true);

// Helper functions for tests
const mockHelpers = {
  // Get messages in queue
  getMessages: () => [...messageQueue],
  
  // Clear message queue
  clearQueue: () => {
    messageQueue = [];
  },
  
  // Add message to queue (for testing consume)
  addMessage: (exchange, routingKey, content, options = {}) => {
    const message = {
      exchange,
      routingKey,
      content: typeof content === 'string' ? content : JSON.stringify(content),
      options,
      timestamp: Date.now()
    };
    messageQueue.push(message);
  },
  
  // Simulate connection failure
  simulateConnectionFailure: () => {
    connect.mockRejectedValueOnce(new Error('Connection failed'));
  },
  
  // Simulate channel failure
  simulateChannelFailure: () => {
    mockConnection.createChannel.mockRejectedValueOnce(new Error('Channel creation failed'));
  },
  
  // Reset all mocks
  reset: () => {
    messageQueue = [];
    mockPublish.mockClear();
    mockConsume.mockClear();
    mockAck.mockClear();
    mockNack.mockClear();
    mockAssertExchange.mockClear();
    mockAssertQueue.mockClear();
    mockBindQueue.mockClear();
    mockPrefetch.mockClear();
    mockClose.mockClear();
    connect.mockClear();
    mockConnection.createChannel.mockClear();
  },
  
  // Get call history
  getPublishCalls: () => mockPublish.mock.calls,
  getConsumeCalls: () => mockConsume.mock.calls,
  getConnectCalls: () => connect.mock.calls
};

module.exports = {
  connect,
  
  // Mock instances for direct access
  mockConnection,
  mockChannel,
  mockPublish,
  mockConsume,
  mockAck,
  mockNack,
  mockAssertExchange,
  mockAssertQueue,
  mockBindQueue,
  mockPrefetch,
  mockClose,
  
  // Helper functions
  ...mockHelpers
};
