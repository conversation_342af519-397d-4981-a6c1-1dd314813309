// Mock Google Generative AI module for testing without API calls

const mockGenerateContent = jest.fn();

// Mock model
const mockModel = {
  generateContent: mockGenerateContent
};

// Mock GoogleGenerativeAI class
class MockGoogleGenerativeAI {
  constructor(apiKey) {
    this.apiKey = apiKey;
  }
  
  getGenerativeModel(config) {
    return mockModel;
  }
}

// Default AI response
const defaultAIResponse = {
  response: {
    text: () => JSON.stringify({
      persona_profile: {
        riasec_scores: {
          realistic: 3.8,
          investigative: 4.2,
          artistic: 3.4,
          social: 4.6,
          enterprising: 3.8,
          conventional: 2.8
        },
        ocean_scores: {
          openness: 4.2,
          conscientiousness: 4.6,
          extraversion: 3.8,
          agreeableness: 4.6,
          neuroticism: 2.4
        },
        via_is_scores: {
          wisdom: 4.3,
          courage: 4.7,
          humanity: 4.3,
          justice: 4.7,
          temperance: 4.3,
          transcendence: 4.3
        },
        multiple_intelligences_scores: {
          linguistic: 4.3,
          logical_mathematical: 4.7,
          spatial: 3.3,
          musical: 3.0,
          bodily_kinesthetic: 3.7,
          interpersonal: 4.7,
          intrapersonal: 4.7,
          naturalistic: 3.3
        },
        cognitive_style_scores: {
          knowing: 4.3,
          planning: 4.7,
          creating: 3.7
        },
        career_recommendations: [
          {
            title: "Software Developer",
            match_percentage: 85,
            reasoning: "High investigative and realistic scores indicate strong problem-solving abilities and hands-on technical skills."
          },
          {
            title: "Data Scientist",
            match_percentage: 82,
            reasoning: "Strong logical-mathematical intelligence combined with investigative traits make this an excellent fit."
          },
          {
            title: "UX/UI Designer",
            match_percentage: 78,
            reasoning: "Combination of artistic and social traits with good spatial intelligence."
          }
        ],
        personality_summary: "You are a thoughtful and analytical person with strong problem-solving abilities. You enjoy working with data and technology, and you have a natural inclination towards helping others. Your high conscientiousness means you are reliable and detail-oriented, while your openness to experience drives you to continuously learn and explore new ideas.",
        strengths: [
          "Analytical thinking and problem-solving",
          "Strong attention to detail",
          "Excellent interpersonal skills",
          "Continuous learning mindset",
          "Reliable and conscientious"
        ],
        development_areas: [
          "Public speaking and presentation skills",
          "Creative expression and artistic pursuits",
          "Stress management and emotional regulation",
          "Leadership and team management",
          "Networking and relationship building"
        ],
        learning_style: "You prefer structured learning environments with clear objectives and practical applications. You learn best through hands-on experience, logical progression, and when you can see the real-world relevance of what you're studying.",
        work_environment: "You thrive in collaborative yet focused environments where you can work both independently and as part of a team. You prefer organized workspaces with minimal distractions and appreciate clear communication and well-defined processes."
      }
    })
  }
};

// Setup default mock behavior
mockGenerateContent.mockResolvedValue(defaultAIResponse);

// Helper functions for tests
const mockHelpers = {
  // Set custom AI response
  setAIResponse: (response) => {
    mockGenerateContent.mockResolvedValueOnce({
      response: {
        text: () => JSON.stringify(response)
      }
    });
  },
  
  // Set AI to fail
  setAIError: (error) => {
    mockGenerateContent.mockRejectedValueOnce(error);
  },
  
  // Set AI to return invalid JSON
  setInvalidResponse: () => {
    mockGenerateContent.mockResolvedValueOnce({
      response: {
        text: () => 'Invalid JSON response'
      }
    });
  },
  
  // Reset all mocks
  reset: () => {
    mockGenerateContent.mockClear();
    mockGenerateContent.mockResolvedValue(defaultAIResponse);
  },
  
  // Get call history
  getGenerateContentCalls: () => mockGenerateContent.mock.calls
};

module.exports = {
  GoogleGenerativeAI: MockGoogleGenerativeAI,
  
  // Mock instances for direct access
  mockModel,
  mockGenerateContent,
  
  // Helper functions
  ...mockHelpers
};
