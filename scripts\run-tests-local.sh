#!/bin/bash

# ATMA Backend Test Runner Script (Local Environment)
# This script runs tests using local PostgreSQL and RabbitMQ installations

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICES=("api-gateway" "auth-service" "assessment-service" "analysis-worker" "archive-service")
TEST_DB_NAME="atma_test_db"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check local services
check_local_services() {
    print_status "Checking local services..."
    
    # Check PostgreSQL
    if command_exists psql; then
        if pg_isready -q; then
            print_success "PostgreSQL is running"
        else
            print_error "PostgreSQL is not running. Please start PostgreSQL service."
            print_status "Windows: Start PostgreSQL service from Services"
            print_status "macOS: brew services start postgresql"
            print_status "Linux: sudo systemctl start postgresql"
            exit 1
        fi
    else
        print_error "PostgreSQL is not installed"
        exit 1
    fi
    
    # Check RabbitMQ (optional for unit tests)
    if command_exists rabbitmqctl; then
        if rabbitmqctl status >/dev/null 2>&1; then
            print_success "RabbitMQ is running"
        else
            print_warning "RabbitMQ is not running. Integration tests may fail."
            print_status "Windows: Start RabbitMQ service from Services"
            print_status "macOS: brew services start rabbitmq"
            print_status "Linux: sudo systemctl start rabbitmq-server"
        fi
    else
        print_warning "RabbitMQ is not installed. Only unit tests will run."
    fi
}

# Function to setup test database
setup_test_database() {
    print_status "Setting up test database..."
    
    # Create test database if it doesn't exist
    if ! psql -lqt | cut -d \| -f 1 | grep -qw $TEST_DB_NAME; then
        print_status "Creating test database: $TEST_DB_NAME"
        createdb $TEST_DB_NAME
    else
        print_status "Test database already exists: $TEST_DB_NAME"
    fi
    
    # Run database migrations for each service
    for service in "${SERVICES[@]}"; do
        if [ -d "$service" ] && [ -f "$service/database/migrations" ]; then
            print_status "Running migrations for $service..."
            cd $service
            # Add migration commands here if they exist
            cd ..
        fi
    done
    
    print_success "Test database setup complete"
}

# Function to run unit tests (no external dependencies)
run_unit_tests() {
    local service=$1
    print_status "Running unit tests for $service..."
    
    cd $service
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies for $service..."
        npm install
    fi
    
    # Set test environment variables
    export NODE_ENV=test
    export DB_HOST=localhost
    export DB_PORT=5432
    export DB_NAME=$TEST_DB_NAME
    export DB_USER=${DB_USER:-postgres}
    export DB_PASSWORD=${DB_PASSWORD:-}
    export RABBITMQ_URL=amqp://localhost:5672
    export JWT_SECRET=test_jwt_secret_key_for_testing_only
    export INTERNAL_SERVICE_KEY=test_internal_service_key_for_testing
    
    # Run unit tests with mocked dependencies
    if npm run test:unit 2>/dev/null; then
        print_success "Unit tests passed for $service"
    elif npm test 2>/dev/null; then
        print_success "Tests passed for $service"
    else
        print_error "Tests failed for $service"
        return 1
    fi
    
    cd ..
}

# Function to run integration tests (requires local services)
run_integration_tests() {
    print_status "Running integration tests..."
    
    # Check if local services are available
    if ! pg_isready -q; then
        print_error "PostgreSQL is required for integration tests"
        return 1
    fi
    
    # Set test environment variables
    export NODE_ENV=test
    export DB_HOST=localhost
    export DB_PORT=5432
    export DB_NAME=$TEST_DB_NAME
    export DB_USER=${DB_USER:-postgres}
    export DB_PASSWORD=${DB_PASSWORD:-}
    export RABBITMQ_URL=amqp://localhost:5672
    export JWT_SECRET=test_jwt_secret_key_for_testing_only
    export INTERNAL_SERVICE_KEY=test_internal_service_key_for_testing
    
    # Run integration tests if they exist
    if [ -f "tests/integration/run-integration-tests.js" ]; then
        node tests/integration/run-integration-tests.js
        print_success "Integration tests completed"
    else
        print_warning "No integration tests found"
    fi
}

# Function to run mocked tests (all external dependencies mocked)
run_mocked_tests() {
    print_status "Running tests with mocked dependencies..."
    
    for service in "${SERVICES[@]}"; do
        if [ -d "$service" ]; then
            print_status "Running mocked tests for $service..."
            cd $service
            
            # Set environment for mocked testing
            export NODE_ENV=test
            export MOCK_EXTERNAL_SERVICES=true
            export DB_HOST=localhost
            export DB_PORT=5432
            export DB_NAME=memory  # Use in-memory database
            export RABBITMQ_URL=mock://localhost
            export JWT_SECRET=test_jwt_secret_key_for_testing_only
            export INTERNAL_SERVICE_KEY=test_internal_service_key_for_testing
            
            # Run tests with mocks
            if npm run test:mock 2>/dev/null || npm test 2>/dev/null; then
                print_success "Mocked tests passed for $service"
            else
                print_warning "Mocked tests failed for $service"
            fi
            
            cd ..
        fi
    done
}

# Function to run security tests
run_security_tests() {
    print_status "Running security tests..."
    
    # Run npm audit for each service
    for service in "${SERVICES[@]}"; do
        if [ -d "$service" ]; then
            print_status "Running security audit for $service..."
            cd $service
            npm audit --audit-level=high || print_warning "Security vulnerabilities found in $service"
            cd ..
        fi
    done
}

# Function to generate test report
generate_test_report() {
    print_status "Generating test report..."
    
    local report_file="test-report-local-$(date +%Y%m%d-%H%M%S).html"
    
    cat > $report_file << EOF
<!DOCTYPE html>
<html>
<head>
    <title>ATMA Backend Test Report (Local)</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007cba; }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <div class="header">
        <h1>ATMA Backend Test Report (Local Environment)</h1>
        <p>Generated on: $(date)</p>
        <p>Test Environment: Local PostgreSQL and RabbitMQ</p>
    </div>
    
    <div class="section success">
        <h2>Test Summary</h2>
        <p>Tests executed using local development environment.</p>
        <p>External dependencies: PostgreSQL (local), RabbitMQ (local/mocked)</p>
    </div>
    
    <div class="section">
        <h2>Services Tested</h2>
        <ul>
EOF

    for service in "${SERVICES[@]}"; do
        echo "            <li>$service</li>" >> $report_file
    done

    cat >> $report_file << EOF
        </ul>
    </div>
</body>
</html>
EOF

    print_success "Test report generated: $report_file"
}

# Main function
main() {
    local test_type=${1:-"unit"}
    
    print_status "Starting ATMA Backend Test Suite (Local Environment)"
    print_status "Test type: $test_type"
    
    case $test_type in
        "unit")
            # Unit tests with mocked dependencies
            for service in "${SERVICES[@]}"; do
                if [ -d "$service" ]; then
                    run_unit_tests $service
                fi
            done
            ;;
        "integration")
            check_local_services
            setup_test_database
            run_integration_tests
            ;;
        "mocked")
            # All tests with mocked external dependencies
            run_mocked_tests
            ;;
        "security")
            run_security_tests
            ;;
        "all")
            # Check what's available and run appropriate tests
            if pg_isready -q; then
                print_status "PostgreSQL available - running integration tests"
                setup_test_database
                run_integration_tests
            else
                print_warning "PostgreSQL not available - running mocked tests only"
                run_mocked_tests
            fi
            
            # Always run unit tests and security tests
            for service in "${SERVICES[@]}"; do
                if [ -d "$service" ]; then
                    run_unit_tests $service
                fi
            done
            
            run_security_tests
            generate_test_report
            ;;
        *)
            print_error "Unknown test type: $test_type"
            echo "Usage: $0 [unit|integration|mocked|security|all]"
            echo ""
            echo "Test types:"
            echo "  unit        - Unit tests with mocked dependencies"
            echo "  integration - Integration tests (requires local PostgreSQL/RabbitMQ)"
            echo "  mocked      - All tests with mocked external services"
            echo "  security    - Security audits and vulnerability scans"
            echo "  all         - Run all available tests based on local environment"
            exit 1
            ;;
    esac
    
    print_success "Test suite completed successfully!"
}

# Run main function with all arguments
main "$@"
