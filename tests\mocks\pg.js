// Mock PostgreSQL (pg) module for testing without database

const mockQuery = jest.fn();
const mockConnect = jest.fn();
const mockEnd = jest.fn();
const mockRelease = jest.fn();

// Mock client
const mockClient = {
  query: mockQuery,
  release: mockRelease,
  end: mockEnd
};

// Mock pool
const mockPool = {
  query: mockQuery,
  connect: jest.fn(() => Promise.resolve(mockClient)),
  end: mockEnd,
  totalCount: 0,
  idleCount: 0,
  waitingCount: 0
};

// Mock Pool constructor
const Pool = jest.fn(() => mockPool);

// Mock Client constructor
const Client = jest.fn(() => mockClient);

// Default query responses
const defaultResponses = {
  // User queries
  'SELECT * FROM auth.users WHERE email = $1': {
    rows: [],
    rowCount: 0
  },
  'INSERT INTO auth.users': {
    rows: [{ 
      id: 'mock-user-id',
      email: '<EMAIL>',
      full_name: 'Test User',
      token_balance: 5,
      created_at: new Date(),
      updated_at: new Date()
    }],
    rowCount: 1
  },
  
  // Analysis result queries
  'SELECT * FROM archive.analysis_results WHERE user_id = $1': {
    rows: [],
    rowCount: 0
  },
  'INSERT INTO archive.analysis_results': {
    rows: [{
      id: 'mock-result-id',
      user_id: 'mock-user-id',
      job_id: 'mock-job-id',
      status: 'pending',
      persona_profile: null,
      created_at: new Date(),
      updated_at: new Date()
    }],
    rowCount: 1
  },
  
  // Assessment queries
  'SELECT * FROM assessment.assessments WHERE id = $1': {
    rows: [],
    rowCount: 0
  }
};

// Setup default mock behavior
mockQuery.mockImplementation((text, params) => {
  // Find matching response based on query text
  for (const [queryPattern, response] of Object.entries(defaultResponses)) {
    if (text.includes(queryPattern) || text.startsWith(queryPattern)) {
      return Promise.resolve(response);
    }
  }
  
  // Default response for unmatched queries
  return Promise.resolve({
    rows: [],
    rowCount: 0
  });
});

mockConnect.mockResolvedValue(mockClient);

// Helper functions for tests
const mockHelpers = {
  // Set custom query response
  setQueryResponse: (query, response) => {
    mockQuery.mockImplementationOnce((text) => {
      if (text.includes(query)) {
        return Promise.resolve(response);
      }
      return Promise.resolve({ rows: [], rowCount: 0 });
    });
  },
  
  // Set query to fail
  setQueryError: (query, error) => {
    mockQuery.mockImplementationOnce((text) => {
      if (text.includes(query)) {
        return Promise.reject(error);
      }
      return Promise.resolve({ rows: [], rowCount: 0 });
    });
  },
  
  // Reset all mocks
  reset: () => {
    mockQuery.mockClear();
    mockConnect.mockClear();
    mockEnd.mockClear();
    mockRelease.mockClear();
  },
  
  // Get call history
  getQueryCalls: () => mockQuery.mock.calls,
  getConnectCalls: () => mockConnect.mock.calls
};

module.exports = {
  Pool,
  Client,
  
  // Mock instances for direct access
  mockPool,
  mockClient,
  mockQuery,
  mockConnect,
  mockEnd,
  mockRelease,
  
  // Helper functions
  ...mockHelpers
};
