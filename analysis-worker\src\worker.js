/**
 * Analysis Worker - Main Entry Point
 * 
 * This worker consumes assessment jobs from RabbitMQ,
 * processes them using Google Generative AI,
 * and saves results to Archive Service.
 */

// Load environment variables
const path = require('path');

// Load environment variables
require('dotenv').config();

// Import dependencies
const logger = require('./utils/logger');
const queueConsumer = require('./services/queueConsumer');
const { gracefulShutdown } = require('./utils/shutdown');

// Log worker startup
logger.info('Analysis Worker starting up', {
  environment: process.env.NODE_ENV,
  queueName: process.env.QUEUE_NAME,
  workerConcurrency: process.env.WORKER_CONCURRENCY
});

/**
 * Main function to start the worker
 */
async function startWorker() {
  try {
    // Initialize queue consumer
    await queueConsumer.initialize();
    
    // Start consuming messages
    await queueConsumer.startConsuming();
    
    // Log successful startup
    logger.info('Analysis Worker started successfully and is consuming messages');
    
    // Setup heartbeat for monitoring
    const heartbeatInterval = parseInt(process.env.HEARTBEAT_INTERVAL || '30000');
    setInterval(() => {
      logger.info('Worker heartbeat', {
        status: 'running',
        timestamp: new Date().toISOString()
      });
    }, heartbeatInterval);
    
  } catch (error) {
    logger.error('Failed to start Analysis Worker', {
      error: error.message,
      stack: error.stack
    });
    
    // Exit with error
    process.exit(1);
  }
}

// Handle process termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', {
    error: error.message,
    stack: error.stack
  });
  gracefulShutdown('uncaughtException');
});

// Start the worker
startWorker();
